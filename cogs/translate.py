import discord
from discord.ext import commands

from utilities import decorators, checks
import config


async def setup(bot):
    await bot.add_cog(Translate(bot))


class Translate(commands.Cog):
    """
    Translation commands using Cloudflare AI.
    """

    def __init__(self, bot):
        self.bot = bot

    async def translate_text(self, text: str, source_lang: str = "en", target_lang: str = "es"):
        """
        Translate text using Cloudflare AI m2m100-1.2b model.
        """
        try:
            account_id = config.CLOUDFLARE.account_id
            api_token = config.CLOUDFLARE.api_token
        except AttributeError:
            return "Cloudflare AI configuration is missing. Please check the config file."

        if account_id == 'f8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8' or len(account_id) != 32:
            return "Please configure your Cloudflare account ID in the config file. You can find it in your Cloudflare dashboard under AI > Workers AI."

        url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/ai/run/@cf/meta/m2m100-1.2b"

        headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json"
        }

        payload = {
            "text": text,
            "source_lang": source_lang,
            "target_lang": target_lang
        }

        try:
            async with self.bot.session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success") and "result" in data:
                        return data["result"].get("translated_text", "Translation failed")
                    else:
                        errors = data.get('errors', [])
                        if errors:
                            error_msg = errors[0].get('message', 'Unknown error') if isinstance(errors, list) else str(errors)
                            return f"API Error: {error_msg}"
                        return "Translation failed: Unknown error"
                elif response.status == 401:
                    return "Authentication failed. Please check your Cloudflare API token."
                elif response.status == 403:
                    return "Access denied. Please check your Cloudflare account permissions."
                else:
                    error_text = await response.text()
                    return f"HTTP {response.status}: {error_text[:200]}"
        except Exception as e:
            return f"Request failed: {str(e)}"

    @decorators.command(
        brief="Translate text using AI.",
        aliases=["tr"],
        examples="""
                {0}translate Hello world
                {0}translate "Bonjour le monde" --from fr --to en
                {0}tr "Hola mundo" --to en
                {0}translate (reply to a message to translate it)
                """,
    )
    @checks.cooldown()
    async def translate(self, ctx, *, text: str = None):
        """
        Usage: {0}translate <text> [--from source_lang] [--to target_lang]
        Alias: {0}tr
        Examples:
            {0}translate Hello world
            {0}translate "Bonjour le monde" --from fr --to en
            {0}tr "Hola mundo" --to en
        Output: Translates text using Cloudflare AI
        Notes:
            - Defaults to translating from English to Spanish
            - You can reply to a message to translate it
            - Use language codes like: en, es, fr, de, it, pt, ru, ja, ko, zh, etc.
        """
        await ctx.trigger_typing()

        # Check if replying to a message
        if ctx.message.reference and not text:
            try:
                replied_message = await ctx.channel.fetch_message(ctx.message.reference.message_id)
                text = replied_message.content
                if not text:
                    return await ctx.send_or_reply("The replied message has no text to translate.")
            except:
                return await ctx.send_or_reply("Could not fetch the replied message.")

        if not text:
            return await ctx.send_or_reply("Please provide text to translate or reply to a message.")

        # Parse language arguments
        source_lang = "en"  # Default source language
        target_lang = "es"  # Default target language

        # Simple argument parsing for --from and --to flags
        if "--from" in text:
            parts = text.split("--from")
            if len(parts) > 1:
                text = parts[0].strip()
                remaining = parts[1].strip()
                if " " in remaining:
                    source_lang = remaining.split()[0]
                    # Check if there's more text after --from lang
                    rest = " ".join(remaining.split()[1:])
                    if "--to" in rest:
                        to_parts = rest.split("--to")
                        if len(to_parts) > 1:
                            target_lang = to_parts[1].strip().split()[0]
                    text = text + " " + rest.replace(f"--to {target_lang}", "").strip()
                else:
                    source_lang = remaining

        if "--to" in text:
            parts = text.split("--to")
            if len(parts) > 1:
                text = parts[0].strip()
                remaining = parts[1].strip()
                if " " in remaining:
                    target_lang = remaining.split()[0]
                else:
                    target_lang = remaining

        # Clean up the text
        text = text.strip().strip('"').strip("'")

        if not text:
            return await ctx.send_or_reply("Please provide text to translate.")

        # Translate the text
        result = await self.translate_text(text, source_lang, target_lang)

        # Create embed for response (no emojis as requested)
        embed = discord.Embed(
            title="Translation",
            color=0x2F3136
        )

        embed.add_field(
            name=f"Original ({source_lang.upper()})",
            value=f"```{text[:1000]}```",
            inline=False
        )

        embed.add_field(
            name=f"Translation ({target_lang.upper()})",
            value=f"```{result[:1000]}```",
            inline=False
        )

        embed.set_footer(text="Powered by Cloudflare AI")

        await ctx.send_or_reply(embed=embed)
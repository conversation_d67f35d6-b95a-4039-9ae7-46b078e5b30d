import discord
from discord.ext import commands
import aiohttp

class Man<PERSON><PERSON>eader(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def search_manga(self, query):
        url = f"https://api.mangadex.org/manga?title={query}&limit=5"
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as resp:
                data = await resp.json()
                if "data" not in data or len(data["data"]) == 0:
                    return []
                return [(m['id'], m['attributes']['title'].get('en', 'Unknown')) for m in data['data']]

    async def fetch_chapters(self, manga_id):
        url = f"https://api.mangadex.org/chapter?manga={manga_id}&translatedLanguage[]=en&limit=10&order[chapter]=asc"
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as resp:
                data = await resp.json()
                return [(c['id'], c['attributes']['title'] or f"Chapter {c['attributes']['chapter']}") for c in data['data']]

    async def fetch_pages(self, chapter_id):
        url = f"https://api.mangadex.org/at-home/server/{chapter_id}"
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as resp:
                data = await resp.json()
                base_url = data['baseUrl']
                chapter_hash = data['chapter']['hash']
                return [f"{base_url}/data/{chapter_hash}/{file}" for file in data['chapter']['data']]

    @commands.command()
    async def manga(self, ctx, *, search_query: str):
        mangas = await self.search_manga(search_query)

        if not mangas:
            await ctx.send("❌ No manga found for that search.")
            return

        options = [discord.SelectOption(label=title, value=manga_id) for manga_id, title in mangas]
        select = discord.ui.Select(placeholder="Select a manga", options=options)

        async def select_callback(interaction):
            chapters = await self.fetch_chapters(select.values[0])
            if not chapters:
                await interaction.response.send_message("❌ No chapters found for this manga.", ephemeral=True)
                return

            chapter_options = [discord.SelectOption(label=title, value=cid) for cid, title in chapters]
            chapter_select = discord.ui.Select(placeholder="Select a chapter", options=chapter_options)

            async def chapter_callback(interaction):
                pages = await self.fetch_pages(chapter_select.values[0])
                if not pages:
                    await interaction.response.send_message("❌ No pages found.", ephemeral=True)
                    return

                page_index = 0
                embed = discord.Embed(title="Manga Reader", description=f"Page {page_index+1}/{len(pages)}")
                embed.set_image(url=pages[page_index])

                prev_btn = discord.ui.Button(label="⬅ Previous", style=discord.ButtonStyle.secondary)
                next_btn = discord.ui.Button(label="Next ➡", style=discord.ButtonStyle.primary)
                jump_btn = discord.ui.Button(label="🔢 Jump to Page", style=discord.ButtonStyle.success)

                async def update_page(interaction):
                    embed.set_image(url=pages[page_index])
                    embed.description = f"Page {page_index+1}/{len(pages)}"
                    await interaction.response.edit_message(embed=embed, view=view)

                async def prev_callback(interaction):
                    nonlocal page_index
                    if page_index > 0:
                        page_index -= 1
                        await update_page(interaction)

                async def next_callback(interaction):
                    nonlocal page_index
                    if page_index < len(pages) - 1:
                        page_index += 1
                        await update_page(interaction)

                async def jump_callback(interaction):
                    modal = discord.ui.Modal(title="Jump to Page")
                    page_input = discord.ui.TextInput(label="Enter page number", placeholder="1", required=True)
                    modal.add_item(page_input)

                    async def modal_callback(interaction):
                        nonlocal page_index
                        try:
                            num = int(page_input.value)
                            if 1 <= num <= len(pages):
                                page_index = num - 1
                                await update_page(interaction)
                            else:
                                await interaction.response.send_message("❌ Invalid page number.", ephemeral=True)
                        except ValueError:
                            await interaction.response.send_message("❌ Please enter a valid number.", ephemeral=True)

                    modal.on_submit = modal_callback
                    await interaction.response.send_modal(modal)

                prev_btn.callback = prev_callback
                next_btn.callback = next_callback
                jump_btn.callback = jump_callback

                view = discord.ui.View()
                view.add_item(prev_btn)
                view.add_item(next_btn)
                view.add_item(jump_btn)

                await interaction.response.send_message(embed=embed, view=view)

            chapter_select.callback = chapter_callback
            chapter_view = discord.ui.View()
            chapter_view.add_item(chapter_select)
            await interaction.response.send_message("Select a chapter:", view=chapter_view)

        select.callback = select_callback
        view = discord.ui.View()
        view.add_item(select)
        await ctx.send("Select a manga:", view=view)

async def setup(bot):
    await bot.add_cog(MangaReader(bot))
